import { Component } from '@angular/core';
import { AccordionComponent } from '../../../../../play-comp-library/src/public-api';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-test-accordion',
  imports: [CommonModule, AccordionComponent],
  templateUrl: './test-accordion.component.html',
  styleUrl: './test-accordion.component.scss'
})
export class TestAccordionComponent {

  public settingsContent = `
    <h4>Account Settings</h4>
    <p>Manage your account preferences and security settings.</p>
    <ul>
      <li>Profile information</li>
      <li>Security settings</li>
      <li>Notification preferences</li>
      <li>Privacy controls</li>
    </ul>
  `;

  onAccordionToggle(event: Event): void {
    console.log('Accordion toggled:', event);
  }
}
